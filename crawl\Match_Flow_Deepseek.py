from crawl4ai.web_crawler import WebCrawler
from crawl4ai.extraction_strategy import *

def extract_match_flow_with_crawl4ai(url):
    try:
        # Initialize the crawler with your Crawl4AI API token
        crawler = WebCrawler(token="YOUR_CRAWL4AI_API_TOKEN")
        
        # Configure the extraction strategy
        strategy = RawHtmlExtractionStrategy(
            include_all_elements=False,
            extraction_rules={
                "match_flow": {
                    "type": "text",
                    "selector": "h2:contains('Match Flow') + div",
                    "output": "text"
                }
            }
        )
        
        # Execute the crawl
        result = crawler.run(
            url=url,
            strategy=strategy,
            bypass_cache=False,
            wait_for_page_load=3  # seconds to wait for page load
        )
        
        # Extract and print the Match Flow content
        if 'match_flow' in result.extracted_data:
            print("\n=== MATCH FLOW ===")
            print(result.extracted_data['match_flow'])
            print("=" * 40)
        else:
            print("Match Flow section not found in the extracted data.")
            
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    url = "https://www.espncricinfo.com/series/ipl-2025-1449924/sunrisers-hyderabad-vs-rajasthan-royals-2nd-match-1473439/full-scorecard"
    extract_match_flow_with_crawl4ai(url)
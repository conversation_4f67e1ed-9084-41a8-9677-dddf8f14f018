import requests
import json

url = "https://scores2.bcci.tv/getDomesticMatchCenterDetails"
params = {
    "seriesSlug": "womens under 23 t20 trophy",
    "matchID": "15003",
    "SERIES_ID": ""
}
headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

try:
    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()
    data = response.json()

    # Extract Competition Name
    competition_name = data.get("postMatch", [{}])[0].get("CompetitionName", "N/A")
    match_order = data.get("postMatch", [{}])[0].get("MatchOrder", "N/A")

    # Extract Umpire Names
    ground_umpire1 = data.get("postMatch", [{}])[0].get("GroundUmpire1", "N/A")
    ground_umpire2 = data.get("postMatch", [{}])[0].get("GroundUmpire2", "N/A")
    third_umpire = data.get("postMatch", [{}])[0].get("ThirdUmpire", "N/A")

    # Print Results
    print(f"Competition Name: {competition_name}")
    print(f"Match Type: {match_order}")
    print(f"Ground Umpire 1: {ground_umpire1}")
    print(f"Ground Umpire 2: {ground_umpire2}")
    print(f"Third Umpire: {third_umpire}")

except Exception as e:
    print(f"Error: {e}")
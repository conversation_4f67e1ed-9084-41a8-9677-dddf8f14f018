# Crawl4AI Guide

## Overview

Crawl4AI is an open-source web crawler and scraper designed to be LLM-friendly. It provides a powerful and flexible API for crawling websites and extracting content in various formats.

## Key Features

- **Asynchronous Crawling**: Built on asyncio for efficient web crawling
- **Browser Automation**: Uses Playwright for browser automation
- **Content Extraction**: Extracts content in multiple formats (HTML, Markdown, text)
- **Configurable**: Extensive configuration options for both browser and crawler
- **Stealth Mode**: Avoid detection by anti-bot systems
- **Link and Image Extraction**: Automatically extract links and images from pages
- **Scrolling Support**: Automatically scroll pages to load dynamic content
- **Authentication Support**: Handle login forms and authenticated sessions

## Installation

```bash
pip install crawl4ai
```

## Basic Usage

```python
import asyncio
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig

async def main():
    browser_config = BrowserConfig()  # Default browser configuration
    run_config = CrawlerRunConfig()   # Default crawl run configuration

    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://example.com",
            config=run_config
        )
        print(result.markdown)  # Print clean markdown content
        print(result.html)      # Print HTML content

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration Options

### Browser Configuration (BrowserConfig)

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| headless | bool | True | Run browser in headless mode |
| browser_type | str | "chromium" | Browser type ("chromium", "firefox", "webkit") |
| viewport_width | int | 1280 | Browser viewport width |
| viewport_height | int | 720 | Browser viewport height |
| user_agent | str | None | Custom user agent string |
| timeout | int | 30000 | Default timeout in milliseconds |
| stealth_mode | bool | False | Enable stealth mode to avoid detection |
| random_user_agent | bool | False | Use random user agent |

### Crawler Run Configuration (CrawlerRunConfig)

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| wait_for_selector | str | None | Wait for selector to be available |
| wait_for_timeout | int | 0 | Wait time after page load (ms) |
| extract_text | bool | True | Extract text content |
| extract_links | bool | False | Extract links from page |
| extract_images | bool | False | Extract images from page |
| extract_metadata | bool | True | Extract page metadata |
| extract_html | bool | True | Extract HTML content |
| extract_markdown | bool | True | Extract Markdown content |
| scroll_to_bottom | bool | False | Scroll to bottom of page |
| max_scrolls | int | 3 | Maximum number of scrolls |

## Result Object

The `result` object returned by `crawler.arun()` contains:

- `url`: The final URL after any redirects
- `title`: Page title
- `text`: Plain text content
- `html`: HTML content
- `markdown`: Markdown content
- `links`: List of links (if extract_links=True)
- `images`: List of images (if extract_images=True)
- `metadata`: Page metadata (if extract_metadata=True)

## Advanced Usage

See the `crawl4ai_guide.py` file for advanced usage examples, including:

1. Basic crawling
2. Custom browser configuration
3. Custom crawler run configuration
4. Handling authentication
5. Crawling multiple pages
6. Saving results
7. Using stealth mode

## Resources

- GitHub Repository: [https://github.com/unclecode/crawl4ai](https://github.com/unclecode/crawl4ai)
- PyPI Package: [https://pypi.org/project/crawl4ai/](https://pypi.org/project/crawl4ai/)

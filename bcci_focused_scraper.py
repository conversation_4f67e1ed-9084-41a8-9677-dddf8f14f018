import requests
import json
import re
import pandas as pd
import time
from datetime import datetime
from collections import defaultdict
import argparse

def safe_strip(value):
    """Handle None values and clean whitespace"""
    return (value or 'N/A').strip()

class BCCIFocusedScraper:
    def __init__(self, competition_ids=None):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Referer": "https://www.bcci.tv/"
        }
        
        # All BCCI competitions
        self.all_competitions = {
            289: "Duleep Trophy",
            290: "Irani Cup", 
            291: "Ranji Trophy",
            292: "<PERSON> Trophy",
            293: "Vijay Hazare Trophy",
            294: "Col C K Nayudu Trophy",
            295: "Mens U23 State A Trophy",
            296: "Vinoo Mankad Trophy",
            297: "Cooch Behar Trophy",
            298: "Vijay Merchant Trophy",
            299: "VIzzy Trophy",
            300: "Senior Womens T20 Trophy",
            301: "Senior Womens T20 Challenger Trophy",
            302: "Senior Womens One Day Trophy",
            303: "Senior Womens One Day Challenger Trophy",
            304: "Womens U23 T20 Trophy",
            305: "Womens U23 One Day Trophy",
            306: "Womens Under 19 T20 Trophy",
            307: "Womens Under 19 One Day Trophy",
            308: "Womens Under 19 T20 Challenger Trophy",
            309: "Womens Under 15 One Day Trophy",
            310: "Senior Womens Multiday",
            311: "Col Ck Nayudu Trophy Winners vs Roi",
            317: "Additional Competition"
        }
        
        # Set competitions to scrape
        if competition_ids:
            self.competitions = {cid: self.all_competitions[cid] for cid in competition_ids if cid in self.all_competitions}
        else:
            self.competitions = self.all_competitions
        
        self.upcoming_matches = []
    
    def list_available_competitions(self):
        """List all available competitions"""
        print("Available competitions:")
        for comp_id, comp_name in self.all_competitions.items():
            print(f"  {comp_id}: {comp_name}")
    
    def is_upcoming_match(self, match_data):
        """Check if a match is upcoming based on match status"""
        match_status = safe_strip(match_data.get('MatchStatus', '')).lower()
        return match_status == 'upcoming'
    
    def extract_match_data(self, match, comp_id, comp_name):
        """Extract the required fields from match data"""
        return {
            'CompetitionID': comp_id,
            'CompetitionName': comp_name,
            'MatchOrder': safe_strip(match.get('MatchOrder')),
            'MatchID': safe_strip(match.get('MatchID')),
            'MatchTypeID': safe_strip(match.get('MatchTypeID')),
            'MatchType': safe_strip(match.get('MatchType')),
            'MatchName': safe_strip(match.get('MatchName')),
            'MatchDate': safe_strip(match.get('MatchDate')),
            'GroundUmpire1': safe_strip(match.get('GroundUmpire1')),
            'GroundUmpire2': safe_strip(match.get('GroundUmpire2')),
            'ThirdUmpire': safe_strip(match.get('ThirdUmpire')),
            'Referee': safe_strip(match.get('Referee')),
            'GroundName': safe_strip(match.get('GroundName'))
        }
    
    def fetch_competition_data(self, comp_id):
        """Fetch match data for a specific competition"""
        try:
            comp_name = self.competitions.get(comp_id, f"Competition {comp_id}")
            url = f"https://scores.bcci.tv/feeds/{comp_id}-matchschedule.js"
            
            print(f"Fetching data for {comp_name} (ID: {comp_id})...")
            response = requests.get(url, headers=self.headers)
            
            if response.status_code != 200:
                print(f"Failed to fetch data for competition {comp_id}: HTTP {response.status_code}")
                return 0
            
            # Clean JSONP response
            json_str = re.sub(r'^MatchSchedule\(|\);?\s*$', '', response.text, flags=re.DOTALL)
            
            try:
                data = json.loads(json_str)
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON for competition {comp_id}: {e}")
                return 0
            
            # Process matches
            matches_processed = 0
            for match in data.get('Matchsummary', []):
                if self.is_upcoming_match(match):
                    match_data = self.extract_match_data(match, comp_id, comp_name)
                    self.upcoming_matches.append(match_data)
                    matches_processed += 1
            
            print(f"Found {matches_processed} upcoming matches for {comp_name}")
            return matches_processed
            
        except Exception as e:
            print(f"Error processing competition {comp_id}: {str(e)}")
            return 0
    
    def scrape_competitions(self):
        """Scrape upcoming matches from selected competitions"""
        print(f"Starting BCCI scraping for {len(self.competitions)} competitions...\n")
        total_matches = 0
        
        for comp_id in self.competitions:
            matches_count = self.fetch_competition_data(comp_id)
            total_matches += matches_count
            
            # Add delay to be respectful to the server
            time.sleep(1)
        
        print(f"\nScraping complete! Found {total_matches} upcoming matches total.")
        return total_matches
    
    def export_to_excel(self, filename="bcci_upcoming_matches.xlsx"):
        """Export the scraped data to Excel with enhanced formatting"""
        if not self.upcoming_matches:
            print("No upcoming matches found to export.")
            return
        
        # Create DataFrame
        df = pd.DataFrame(self.upcoming_matches)
        
        # Sort by CompetitionID and MatchDate
        df = df.sort_values(['CompetitionID', 'MatchDate'])
        
        # Export to Excel with formatting
        with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Upcoming Matches', index=False)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Upcoming Matches']
            
            # Add formatting
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#FFFF00',
                'valign': 'vcenter',
                'align': 'center',
                'text_wrap': True,
                'border': 1
            })
            
            # Data format
            data_format = workbook.add_format({
                'valign': 'vcenter',
                'align': 'center',
                'border': 1
            })
            
            # Apply header formatting
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            
            # Set column widths and apply data formatting
            column_widths = {
                'CompetitionID': 12,
                'CompetitionName': 25,
                'MatchOrder': 15,
                'MatchID': 10,
                'MatchTypeID': 12,
                'MatchType': 20,
                'MatchName': 30,
                'MatchDate': 12,
                'GroundUmpire1': 20,
                'GroundUmpire2': 20,
                'ThirdUmpire': 20,
                'Referee': 20,
                'GroundName': 25
            }
            
            for col_num, column in enumerate(df.columns):
                width = column_widths.get(column, 15)
                worksheet.set_column(col_num, col_num, width, data_format)
        
        print(f"Data exported to {filename}")
        print(f"Total upcoming matches exported: {len(df)}")
        return df
    
    def print_summary(self):
        """Print a summary of the scraped data"""
        if not self.upcoming_matches:
            print("No upcoming matches found.")
            return
        
        print(f"\n=== UPCOMING MATCHES SUMMARY ===")
        print(f"Total upcoming matches: {len(self.upcoming_matches)}")
        
        # Group by competition
        competition_counts = defaultdict(int)
        for match in self.upcoming_matches:
            competition_counts[match['CompetitionName']] += 1
        
        print("\nMatches by competition:")
        for comp_name, count in sorted(competition_counts.items()):
            print(f"  {comp_name}: {count} matches")

def main():
    """Main function with command line argument support"""
    parser = argparse.ArgumentParser(description='BCCI Upcoming Matches Scraper')
    parser.add_argument('--competitions', '-c', nargs='+', type=int, 
                       help='Competition IDs to scrape (e.g., 291 292 317)')
    parser.add_argument('--list', '-l', action='store_true', 
                       help='List available competitions')
    parser.add_argument('--output', '-o', default='bcci_upcoming_matches.xlsx',
                       help='Output Excel filename')
    
    args = parser.parse_args()
    
    scraper = BCCIFocusedScraper()
    
    if args.list:
        scraper.list_available_competitions()
        return
    
    if args.competitions:
        scraper = BCCIFocusedScraper(args.competitions)
        print(f"Scraping competitions: {args.competitions}")
    
    # Scrape competitions
    scraper.scrape_competitions()
    
    # Print summary
    scraper.print_summary()
    
    # Export to Excel
    scraper.export_to_excel(args.output)

if __name__ == "__main__":
    main()

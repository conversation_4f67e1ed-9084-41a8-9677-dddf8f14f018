"""
News Crawler Example

This script demonstrates how to use crawl4ai to crawl news websites
and extract article content, headlines, and metadata.
"""

import asyncio
import json
import os
from datetime import datetime
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig

# News sites to crawl
NEWS_SITES = [
    "https://news.ycombinator.com",
    "https://www.theverge.com",
    "https://techcrunch.com",
]

# Output directory
OUTPUT_DIR = "news_output"

async def crawl_news_site(crawler, url):
    """Crawl a news site and extract headlines and links."""
    print(f"Crawling: {url}")
    
    # Configuration for the initial page
    run_config = CrawlerRunConfig(
        extract_links=True,
        extract_metadata=True,
        scroll_to_bottom=True,
        max_scrolls=3,
    )
    
    # Crawl the main page
    result = await crawler.arun(url=url, config=run_config)
    
    # Extract domain for file naming
    domain = url.split("//")[1].split("/")[0].replace("www.", "")
    
    # Create a dictionary to store the results
    site_data = {
        "site": url,
        "title": result.title,
        "crawl_time": datetime.now().isoformat(),
        "articles": []
    }
    
    # Extract links that might be articles
    article_links = []
    for link in result.links:
        # Simple heuristic to identify article links
        # This will need to be customized for each site
        if link.get("url") and domain in link.get("url"):
            if any(keyword in link.get("url", "") for keyword in ["/article/", "/post/", "/story/", "/item?id="]):
                article_links.append(link.get("url"))
    
    # Limit to 5 articles for demonstration
    article_links = article_links[:5]
    print(f"Found {len(article_links)} potential articles")
    
    # Configuration for article pages
    article_config = CrawlerRunConfig(
        extract_metadata=True,
        wait_for_timeout=2000,  # Wait 2 seconds for dynamic content
    )
    
    # Crawl each article
    for i, article_url in enumerate(article_links):
        try:
            print(f"  Crawling article {i+1}/{len(article_links)}: {article_url}")
            article_result = await crawler.arun(url=article_url, config=article_config)
            
            # Extract article data
            article_data = {
                "url": article_result.url,
                "title": article_result.title,
                "content_preview": article_result.text[:200] + "..." if article_result.text else "",
                "metadata": article_result.metadata,
            }
            
            site_data["articles"].append(article_data)
            
            # Save the article content as markdown
            article_filename = f"{domain}_article_{i+1}.md"
            with open(os.path.join(OUTPUT_DIR, article_filename), "w", encoding="utf-8") as f:
                f.write(f"# {article_result.title}\n\n")
                f.write(f"Source: [{article_result.url}]({article_result.url})\n\n")
                f.write(article_result.markdown)
            
        except Exception as e:
            print(f"  Error crawling article: {e}")
    
    # Save the site data as JSON
    site_filename = f"{domain}_data.json"
    with open(os.path.join(OUTPUT_DIR, site_filename), "w", encoding="utf-8") as f:
        json.dump(site_data, f, indent=2)
    
    return site_data

async def main():
    """Main function to crawl multiple news sites."""
    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Browser configuration
    browser_config = BrowserConfig(
        headless=True,
        stealth_mode=True,
        timeout=60000,  # 60 seconds timeout
    )
    
    # Results storage
    all_sites_data = []
    
    # Start the crawler
    async with AsyncWebCrawler(config=browser_config) as crawler:
        for site_url in NEWS_SITES:
            try:
                site_data = await crawl_news_site(crawler, site_url)
                all_sites_data.append(site_data)
            except Exception as e:
                print(f"Error crawling {site_url}: {e}")
    
    # Save summary of all sites
    with open(os.path.join(OUTPUT_DIR, "news_summary.json"), "w", encoding="utf-8") as f:
        json.dump({
            "crawl_time": datetime.now().isoformat(),
            "sites_crawled": len(all_sites_data),
            "sites": all_sites_data
        }, f, indent=2)
    
    print(f"\nCrawling complete! Results saved to {OUTPUT_DIR}/")

if __name__ == "__main__":
    print("News Crawler Example")
    print("===================")
    asyncio.run(main())

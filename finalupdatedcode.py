import requests
import json
import re
import csv
import time
import pandas as pd
import xlsxwriter
from collections import defaultdict

# Configuration
competitions = {
    289: "Du<PERSON>p Trophy",
    290: "Irani Cup",
    291: "Ranji Trophy",
    # 292: "<PERSON>",
    # 293: "Vijay <PERSON>",
    # 294: "Col C K Nayudu Trophy",
    # 295: "Mens U23 State A Trophy",
    # 296: "Vinoo Mankad Trophy",
    # 297: "Cooch Behar Trophy",
    # 298: "Vijay Merchant Trophy",
    # 299: "VIzzy Trophy",
    # 300: "Senior Womens T20 Trophy",
    # 301: "Senior Womens T20 Challenger Trophy",
    # 302: "Senior Womens One Day Trophy",
    # 303: "Senior Womens One Day Challenger Trophy",
    # 304: "Womens U23 T20 Trophy",
    # 305: "Womens U23 One Day Trophy",
    # 306: "Womens Under 19 T20 Trophy",
    # 307: "Womens Under 19 One Day Trophy",
    # 308: "Womens Under 19 T20 Challenger Trophy",
    # 309: "Womens Under 15 One Day Trophy",
    # 310: "Senior Womens Multiday",
    # 311: "Col Ck <PERSON> Trophy Winners vs Roi"
}

tournament_days = {
    "Duleep Trophy": 4,
    "Irani Cup": 5,
    "Ranji Trophy": 4,
    "Syed <PERSON>": 0.5,
    "Vijay Hazare Trophy": 1,
    "Col C K Nayudu Trophy": 4,
    "Mens U23 State A Trophy": 1,
    "Vinoo Mankad Trophy": 1,
    "Cooch Behar Trophy": 4,
    "Vijay Merchant Trophy": 3,
    "VIzzy Trophy": 1,
    "Senior Womens T20 Trophy": 0.5,
    "Senior Womens T20 Challenger Trophy": 0.5,
    "Senior Womens One Day Trophy": 1,
    "Senior Womens One Day Challenger Trophy": 1,
    "Womens U23 T20 Trophy": 0.5,
    "Womens U23 One Day Trophy": 1,
    "Womens Under 19 T20 Trophy": 0.5,
    "Womens Under 19 One Day Trophy": 1,
    "Womens Under 19 T20 Challenger Trophy": 0.5,
    "Womens Under 15 One Day Trophy": 1,
    "Senior Womens Multiday": 3,
    "Col Ck Nayudu Trophy Winners vs Roi": 4
}

def get_days_per_match(comp_id, comp_name, match_order):
    """Calculate days based on competition ID and MatchOrder"""
    match_order = (match_order or "").strip().lower()
    
    # Special handling for Ranji Trophy (291)
    if comp_id == 291:
        if any(kw in match_order for kw in ["quarter final", "semi final", "final", "plate - final"]):
            return 5
            
    # Special handling for Vijay Merchant Trophy (298)
    elif comp_id == 298:
        if any(kw in match_order for kw in ["pre quarter final", "quarter final", "semi final", "final", "plate - final"]):
            return 4
    
    # Default to tournament_days mapping
    return tournament_days.get(comp_name, 0)

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Referer": "https://www.bcci.tv/"
}

# Modified data structure to track days per tournament per umpire
umpire_tournament_days = defaultdict(lambda: defaultdict(float))
referee_tournament_days = defaultdict(lambda: defaultdict(float))
all_matches = []

def safe_strip(value):
    """Handle None values and clean whitespace"""
    return (value or 'N/A').strip()

def process_competition(comp_id):
    try:
        comp_name = competitions[comp_id]
        
        url = f"https://scores.bcci.tv/feeds/{comp_id}-matchschedule.js"
        response = requests.get(url, headers=headers)
        
        # Clean JSONP response
        json_str = re.sub(r'^MatchSchedule\(|\);?\s*$', '', response.text, flags=re.DOTALL)
        data = json.loads(json_str)
        
        # Process matches
        for match in data.get('Matchsummary', []):
            match_order = safe_strip(match.get('MatchOrder'))
            days_per_match = get_days_per_match(comp_id, comp_name, match_order)
            
            match_record = {
                "Comp ID": comp_id,
                "Comp Name": comp_name,
                "Match ID": match.get('MatchID'),
                "Match Name": safe_strip(match.get('MatchName')),
                "Match Order": match.get('MatchOrder'),
                "Date": safe_strip(match.get('MatchDate')),
                "First Umpire": safe_strip(match.get('GroundUmpire1')),
                "Second Umpire": safe_strip(match.get('GroundUmpire2')),
                "Third Umpire": safe_strip(match.get('ThirdUmpire')),
                "Referee": safe_strip(match.get('Referee')),
                "Venue": safe_strip(match.get('GroundName')),
                #"Result": safe_strip(match.get('Commentss')),
                "Days Per Match": days_per_match
            }
            all_matches.append(match_record)
            
            # Update umpire days with tournament-specific tracking
            for umpire in [match_record['First Umpire'], 
                          match_record['Second Umpire'], 
                          match_record['Third Umpire']]:
                if umpire != 'N/A':
                    umpire_tournament_days[umpire][comp_name] += days_per_match
            # Update referee days
            for referee in [match_record['Referee']]:
                if referee != 'N/A':
                    referee_tournament_days[referee][comp_name] += days_per_match
            
        print(f"Processed {len(data['Matchsummary'])} matches for {comp_name}")
            
    except Exception as e:
        print(f"Error processing {comp_id}: {str(e)}")
    finally:
        time.sleep(3)

# Main execution
print("Starting tournament processing...\n")
for comp_id in competitions:
    process_competition(comp_id)

# Write results to CSV
# Create DataFrames with sorting and rounding
def create_sorted_df(data_dict, competitions_list):
    rows = []
    for name, tournaments in data_dict.items():
        row = {'Name': name}
        total = 0
        for comp in competitions_list:
            days = round(tournaments.get(comp, 0), 1)
            row[comp] = days
            total += days
        row['Total'] = round(total, 1)
        rows.append(row)
    
    df = pd.DataFrame(rows, columns=['Name'] + competitions_list + ['Total'])
    return df.sort_values('Total', ascending=False)

# Create formatted DataFrames
competition_names = list(competitions.values())
df_umpires = create_sorted_df(umpire_tournament_days, competition_names)
df_referees = create_sorted_df(referee_tournament_days, competition_names)
df_matches = pd.DataFrame(all_matches)

# Excel formatting setup
# ... [Keep all previous code until Excel formatting section] ...

# Modified Excel formatting function
def create_excel_format(writer):
    workbook = writer.book
    
    # Header format (yellow background, centered, bold)
    header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#FFFF00',
        'valign': 'vcenter',
        'align': 'center',
        'text_wrap': True
    })
    
    # Data format with right border
    data_format = workbook.add_format({
        'valign': 'vcenter',
        'align': 'center',
        #'text_wrap': True,
        'right': 1,
        'num_format': '0.0'
    })
    
    # Special border formats
    top_border = workbook.add_format({'top': 2})
    bottom_border = workbook.add_format({'bottom': 2})
    right_border = workbook.add_format({'right': 2})
    left_border = workbook.add_format({'left': 2})

    return header_format, data_format, top_border, bottom_border, right_border, left_border

# Function to convert names to proper case
def proper_case(name):
    return ' '.join(word.capitalize() for word in name.split())

# Apply proper case to the names in the first column
df_umpires['Name'] = df_umpires['Name'].apply(proper_case)
df_referees['Name'] = df_referees['Name'].apply(proper_case)

# Export to Excel with precise formatting
with pd.ExcelWriter('bcci_data_one.xlsx', engine='xlsxwriter') as writer:
    # Write data frames
    df_matches.to_excel(writer, sheet_name='All Matches', index=False)
    df_umpires.to_excel(writer, sheet_name='Umpire Days', index=False)
    df_referees.to_excel(writer, sheet_name='Referee Days', index=False)
    
    # Get formatting objects
    header_format, data_format, top_border, bottom_border, right_border, left_border = create_excel_format(writer)
    workbook = writer.book

    # Apply formatting
    def format_worksheet(worksheet, df):
        # Set column widths and alignment
        worksheet.set_column(0, 0, 15, workbook.add_format({'align': 'left', 'valign': 'vcenter'}))  # First column left-aligned
        worksheet.set_column(1, len(df.columns) - 1, 10, data_format)  # Other columns centered
        
        # Apply headers formatting with borders on all sides
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
            worksheet.conditional_format(0, col_num, 0, col_num, {
                'type': 'no_blanks',
                'format': workbook.add_format({
                    'bold': True,
                    'bg_color': '#FFFF00',
                    'valign': 'vcenter',
                    'align': 'center',
                    'top': 2, 'bottom': 2, 'left': 2, 'right': 2,
                    'text_wrap': True
                })
            })
        
        # Apply borders to data cells
        max_row = len(df)
        max_col = len(df.columns) - 1
        
        # Right border for all columns
        worksheet.conditional_format(1, 0, max_row, max_col, {
            'type': 'no_blanks',
            'format': right_border
        })

        worksheet.conditional_format(1, 0, max_row, max_col, {
            'type': 'no_blanks',
            'format': left_border
        })
        
        # Bottom border for last row
        worksheet.conditional_format(max_row, 0, max_row, max_col, {
            'type': 'no_blanks',
            'format': bottom_border
        })
        
        # Hide gridlines and unused areas
        worksheet.hide_gridlines(2)
        worksheet.set_default_row(hide_unused_rows=True)
        worksheet.set_selection(max_row + 1, 0, max_row + 1, max_col + 1)
        worksheet.set_row(max_row + 2, None, None, {'hidden': True})  # Hide rows after last row
        worksheet.set_column(max_col + 2, 16383, None, None, {'hidden': True})  # Hide columns after last column

        # Format each sheet
    for sheet_name in writer.sheets:
        worksheet = writer.sheets[sheet_name]
        if sheet_name == 'All Matches':
            format_worksheet(worksheet, df_matches)
        elif sheet_name == 'Umpire Days':
            format_worksheet(worksheet, df_umpires)
        elif sheet_name == 'Referee Days':
            format_worksheet(worksheet, df_referees)

print("\nProcessing complete!")
print("Processing complete! File saved as bcci_data.xlsx")

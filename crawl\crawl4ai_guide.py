"""
Crawl4AI Comprehensive Guide

This file demonstrates various features and capabilities of the crawl4ai package.
"""

import asyncio
import os
from crawl4ai import Async<PERSON>eb<PERSON>rawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig

# Example 1: Basic Crawling
async def basic_crawling():
    """Basic example of crawling a single page."""
    print("\n=== Example 1: Basic Crawling ===")
    
    # Default configurations
    browser_config = BrowserConfig()
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://www.espncricinfo.com/series/ipl-2025-1449924/sunrisers-hyderabad-vs-rajasthan-royals-2nd-match-1473439/full-scorecard",
            config=run_config
        )
        
        print(f"Title: {result.title}")
        print(f"URL: {result.url}")
        print(f"Markdown (first 200 chars): {result.markdown[:200]}...")
        print(f"HTML size: {len(result.html)} bytes")

# Example 2: Custom Browser Configuration
async def custom_browser_config():
    """Example with custom browser configuration."""
    print("\n=== Example 2: Custom Browser Configuration ===")
    
    # Custom browser configuration
    browser_config = BrowserConfig(
        headless=False,  # Show the browser window
        browser_type="firefox",  # Use Firefox instead of default
        viewport_width=1280,
        viewport_height=800,
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        timeout=30000,  # 30 seconds timeout
    )
    
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://news.ycombinator.com",
            config=run_config
        )
        
        print(f"Title: {result.title}")
        print(f"URL: {result.url}")
        print(f"Markdown (first 200 chars): {result.markdown[:200]}...")

# Example 3: Custom Crawler Run Configuration
async def custom_run_config():
    """Example with custom crawler run configuration."""
    print("\n=== Example 3: Custom Crawler Run Configuration ===")
    
    browser_config = BrowserConfig(headless=True)
    
    # Custom crawler run configuration
    run_config = CrawlerRunConfig(
        wait_for_selector="main",  # Wait for main element to load
        wait_for_timeout=5000,  # Wait 5 seconds after page load
        extract_text=True,
        extract_links=True,
        extract_images=True,
        extract_metadata=True,
        extract_html=True,
        extract_markdown=True,
        scroll_to_bottom=True,  # Scroll to bottom of page
        max_scrolls=5,  # Maximum number of scrolls
    )
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://github.com/unclecode/crawl4ai",
            config=run_config
        )
        
        print(f"Title: {result.title}")
        print(f"Links found: {len(result.links)}")
        print(f"Images found: {len(result.images)}")
        print(f"Metadata: {result.metadata}")

# Example 4: Handling Authentication
async def handle_authentication():
    """Example of handling a site that requires authentication."""
    print("\n=== Example 4: Handling Authentication ===")
    
    browser_config = BrowserConfig(headless=True)
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # First, navigate to login page
        await crawler.goto("https://github.com/login")
        
        # Fill in login form (replace with your credentials)
        # Note: For security, use environment variables in production
        # await crawler.fill_form("#login_field", os.getenv("GITHUB_USERNAME"))
        # await crawler.fill_form("#password", os.getenv("GITHUB_PASSWORD"))
        # await crawler.click("input[type='submit']")
        
        # Wait for navigation to complete
        # await crawler.wait_for_navigation()
        
        # Now crawl a protected page
        result = await crawler.arun(
            url="https://github.com/settings/profile",
            config=run_config
        )
        
        print(f"Title: {result.title}")
        print(f"URL: {result.url}")
        # Check if we're still on the login page
        if "login" in result.url:
            print("Authentication failed or not provided")
        else:
            print("Successfully authenticated")

# Example 5: Crawling Multiple Pages
async def crawl_multiple_pages():
    """Example of crawling multiple pages."""
    print("\n=== Example 5: Crawling Multiple Pages ===")
    
    browser_config = BrowserConfig(headless=True)
    run_config = CrawlerRunConfig(extract_links=True)
    
    urls = [
        "https://example.com",
        "https://www.python.org",
        "https://github.com/unclecode/crawl4ai"
    ]
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        results = []
        
        for url in urls:
            print(f"Crawling: {url}")
            result = await crawler.arun(url=url, config=run_config)
            results.append(result)
            print(f"  - Title: {result.title}")
            print(f"  - Links: {len(result.links)}")
    
    print(f"Total pages crawled: {len(results)}")

# Example 6: Saving Results
async def save_results():
    """Example of saving crawl results to files."""
    print("\n=== Example 6: Saving Results ===")
    
    browser_config = BrowserConfig(headless=True)
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://example.com",
            config=run_config
        )
        
        # Create output directory if it doesn't exist
        os.makedirs("crawl_output", exist_ok=True)
        
        # Save markdown
        with open("crawl_output/example.md", "w", encoding="utf-8") as f:
            f.write(result.markdown)
        
        # Save HTML
        with open("crawl_output/example.html", "w", encoding="utf-8") as f:
            f.write(result.html)
        
        print(f"Saved markdown to: crawl_output/example.md")
        print(f"Saved HTML to: crawl_output/example.html")

# Example 7: Using Stealth Mode
async def stealth_mode():
    """Example of using stealth mode to avoid detection."""
    print("\n=== Example 7: Using Stealth Mode ===")
    
    browser_config = BrowserConfig(
        headless=True,
        stealth_mode=True,  # Enable stealth mode
        random_user_agent=True,  # Use random user agent
    )
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://bot.sannysoft.com",  # Bot detection test site
            config=run_config
        )
        
        print(f"Title: {result.title}")
        print(f"URL: {result.url}")
        print(f"Markdown size: {len(result.markdown)} bytes")
        print("Check crawl_output/bot_test.html to see if we passed the bot tests")
        
        # Save the result for inspection
        os.makedirs("crawl_output", exist_ok=True)
        with open("crawl_output/bot_test.html", "w", encoding="utf-8") as f:
            f.write(result.html)

# Run all examples
async def run_all_examples():
    await basic_crawling()
    # Uncomment the examples you want to run
    # await custom_browser_config()  # Shows browser window
    await custom_run_config()
    # await handle_authentication()  # Requires credentials
    await crawl_multiple_pages()
    await save_results()
    await stealth_mode()

if __name__ == "__main__":
    print("Crawl4AI Examples")
    print("=================")
    asyncio.run(run_all_examples())

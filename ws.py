import requests
from bs4 import BeautifulSoup
import pandas as pd
import os

# Ensure /mnt/data directory exists
# if not os.path.exists('/mnt/data'):
#     os.makedirs('/mnt/data')

# URL for the specific match
MATCH_URL = "https://www.bcci.tv/domestic/womens-under-19-one-day-trophy/match/15323"

# Function to scrape umpire details from the match page
def scrape_single_match_umpire_data(match_url):
    response = requests.get(match_url)
    match_data = []

    if response.status_code == 200:
        soup = BeautifulSoup(response.text, 'html.parser')
        match_id = match_url.split('/')[-1]
        competition_name = "Women's Under-19 One Day Trophy"

        # Enhanced logic to find umpire details
        umpire_info = "N/A"
        umpire_section = soup.find('ul', class_='match-details-wrap list-unstyled')
        
        if umpire_section:
            umpire_items = umpire_section.find_all('li', class_='md_list')
            for item in umpire_items:
                span = item.find('span')
                if span and 'On Field Umpires' in span.text:
                    next_span = item.find_all('span')[-1]  # Last span should contain umpire names
                    umpire_info = next_span.text.strip()
                    break
        
        match_data.append([match_id, competition_name, umpire_info])
        print(f"✅ Scraped data for Match ID: {match_id} | Umpires: {umpire_info}")
    else:
        print(f"⚠️ Failed to fetch match page: {match_url}")

    return match_data

# Save the data to CSV
def save_to_csv(data):
    df = pd.DataFrame(data, columns=['Match ID', 'Competition Name', 'On Field Umpires'])
    csv_path = 'womens_u19_umpire_data_single_match3.csv'
    df.to_csv(csv_path, index=False)
    print(f"\n📁 Data saved to {csv_path}")

if __name__ == "__main__":
    print("📊 Scraping umpire data for Match ID 15323...")
    umpire_data = scrape_single_match_umpire_data(MATCH_URL)
    save_to_csv(umpire_data)

"""
Data Extraction Example

This script demonstrates how to use crawl4ai to extract structured data from websites.
"""

import asyncio
import csv
import os
from crawl4ai import Async<PERSON>ebCrawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig

# Target URL - GitHub trending repositories
TARGET_URL = "https://github.com/trending"

async def extract_github_trending():
    """Extract trending repositories from GitHub."""
    print(f"Extracting trending repositories from {TARGET_URL}")
    
    # Browser configuration
    browser_config = BrowserConfig(
        headless=True,
        stealth_mode=True,
    )
    
    # Crawler configuration
    run_config = CrawlerRunConfig(
        wait_for_selector=".Box-row",  # Wait for repository rows to load
        extract_html=True,
    )
    
    # Create output directory
    os.makedirs("data_output", exist_ok=True)
    
    # Initialize data structure
    repositories = []
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Navigate to the page
        result = await crawler.arun(url=TARGET_URL, config=run_config)
        
        # Use <PERSON><PERSON>'s page object for more precise extraction
        page = crawler.page
        
        # Extract repository information using Playwright's selectors
        repo_elements = await page.query_selector_all("article.Box-row")
        
        for repo_element in repo_elements:
            try:
                # Extract repository name
                name_element = await repo_element.query_selector("h2 a")
                full_name = await name_element.inner_text() if name_element else "Unknown"
                full_name = full_name.strip().replace("\n", "").replace(" ", "")
                
                # Extract repository URL
                href = await name_element.get_attribute("href") if name_element else ""
                repo_url = f"https://github.com{href}" if href else ""
                
                # Extract description
                desc_element = await repo_element.query_selector("p")
                description = await desc_element.inner_text() if desc_element else ""
                
                # Extract programming language
                lang_element = await repo_element.query_selector("[itemprop='programmingLanguage']")
                language = await lang_element.inner_text() if lang_element else "Unknown"
                
                # Extract stars
                stars_element = await repo_element.query_selector("a.Link--muted:nth-of-type(1)")
                stars_text = await stars_element.inner_text() if stars_element else "0"
                stars = stars_text.strip().replace(",", "")
                
                # Extract forks
                forks_element = await repo_element.query_selector("a.Link--muted:nth-of-type(2)")
                forks_text = await forks_element.inner_text() if forks_element else "0"
                forks = forks_text.strip().replace(",", "")
                
                # Add to repositories list
                repositories.append({
                    "name": full_name,
                    "url": repo_url,
                    "description": description,
                    "language": language,
                    "stars": stars,
                    "forks": forks
                })
                
                print(f"Extracted: {full_name} ({language})")
                
            except Exception as e:
                print(f"Error extracting repository: {e}")
    
    # Save to CSV
    csv_path = "data_output/github_trending.csv"
    with open(csv_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["name", "url", "description", "language", "stars", "forks"])
        writer.writeheader()
        writer.writerows(repositories)
    
    print(f"\nExtracted {len(repositories)} repositories")
    print(f"Data saved to {csv_path}")
    
    return repositories

async def main():
    """Main function."""
    print("Data Extraction Example")
    print("======================")
    
    await extract_github_trending()

if __name__ == "__main__":
    asyncio.run(main())

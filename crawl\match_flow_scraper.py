import asyncio
from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from crawl4ai.markdown_generation_strategy import <PERSON><PERSON><PERSON>M<PERSON>downGenerator
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy

async def main():
    browser_config = BrowserConfig(
        verbose=True
    )

    md_generator = DefaultMarkdownGenerator(
        options={
            "ignore_links": True,
            "escape_html": False,
            "body_width": 0
        }
    )

    run_config = CrawlerRunConfig(
        word_count_threshold=0,
        excluded_tags=["form"],
        exclude_external_links=True,
        keep_data_attributes=True,
        remove_overlay_elements=True,
        process_iframes=True,
        scraping_strategy=LXMLWebScrapingStrategy(),
        markdown_generator=md_generator
    )

    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url = "https://www.espncricinfo.com/series/ipl-2025-1449924/delhi-capitals-vs-lucknow-super-giants-4th-match-1473441/full-scorecard",
            config = run_config
        )

        if result.success:
            lines = result.markdown.split('\n')
            
            # Find the line that indicates second innings start
            innings_break_index = -1
            for i, line in enumerate(lines):
                if "Delhi Capitals innings" in line:
                    innings_break_index = i
                    break
            
            # Extract DRS reviews with over numbers
            drs_reviews = [line.strip() for line in lines 
                         if "Review by" in line]
            
            # Find the index where each review appears in the original text
            review_indices = []
            for review in drs_reviews:
                for i, line in enumerate(lines):
                    if review in line:
                        review_indices.append(i)
                        break
            
            # Separate reviews by innings based on their position relative to innings_break_index
            first_innings_reviews = []
            second_innings_reviews = []
            
            for review, index in zip(drs_reviews, review_indices):
                if index < innings_break_index:
                    first_innings_reviews.append(review)
                else:
                    second_innings_reviews.append(review)
            
            print("\nDRS Reviews by Innings:")
            print("\nFirst Innings (Lucknow Super Giants):")
            print("-" * 50)
            for review in first_innings_reviews:
                review = review.replace("* Over ", "Over ")
                print(review)
                print("-" * 50)
            
            print("\nSecond Innings (Delhi Capitals):")
            print("-" * 50)
            for review in second_innings_reviews:
                review = review.replace("* Over ", "Over ")
                print(review)
                print("-" * 50)
            
            # Summary
            print(f"\nTotal DRS reviews in the match: {len(drs_reviews)}")
            print(f"First Innings reviews: {len(first_innings_reviews)}")
            print(f"Second Innings reviews: {len(second_innings_reviews)}")
            
            # Count reviews by team
            dc_reviews = sum(1 for review in drs_reviews if "Delhi Capitals" in review)
            lsg_reviews = sum(1 for review in drs_reviews if "Lucknow Super Giants" in review)
            
            print(f"\nReviews by team:")
            print(f"Delhi Capitals: {dc_reviews}")
            print(f"Lucknow Super Giants: {lsg_reviews}")
            
        else:
            print(f"Crawl failed: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(main())

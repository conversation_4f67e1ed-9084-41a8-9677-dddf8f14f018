import requests
import json
import re
import csv
import time
from collections import defaultdict

# Configuration
competitions = {
    289: "Duleep Trophy",
    290: "Irani Cup",
    291: "Ranji Trophy",
    292: "<PERSON>",
    293: "<PERSON>",
    294: "Col C K Nayudu Trophy",
    295: "Mens U23 State A Trophy",
    296: "Vinoo Mankad Trophy",
    297: "Cooch Behar Trophy",
    298: "Vijay Merchant Trophy",
    299: "VIzzy Trophy",
    300: "Senior Womens T20 Trophy",
    301: "Senior Womens T20 Challenger Trophy",
    302: "Senior Womens One Day Trophy",
    303: "Senior Womens One Day Challenger Trophy",
    304: "Womens U23 T20 Trophy",
    305: "Womens U23 One Day Trophy",
    306: "Womens Under 19 T20 Trophy",
    307: "Womens Under 19 One Day Trophy",
    308: "Womens Under 19 T20 Challenger Trophy",
    309: "Womens Under 15 One Day Trophy",
    310: "Senior Womens Multiday",
    311: "Col Ck Nayudu Trophy Winners vs Roi"
}

tournament_days = {
    "Duleep Trophy": 4,
    "Irani Cup": 5,
    "Ranji Trophy": 4,
    "<PERSON>": 0.5,
    "Vijay <PERSON> Trophy": 1,
    "Col C K Nayudu Trophy": 4,
    "Mens U23 State A Trophy": 1,
    "Vinoo Mankad Trophy": 1,
    "Cooch Behar Trophy": 4,
    "Vijay Merchant Trophy": 3,
    "VIzzy Trophy": 1,
    "Senior Womens T20 Trophy": 0.5,
    "Senior Womens T20 Challenger Trophy": 0.5,
    "Senior Womens One Day Trophy": 1,
    "Senior Womens One Day Challenger Trophy": 1,
    "Womens U23 T20 Trophy": 0.5,
    "Womens U23 One Day Trophy": 1,
    "Womens Under 19 T20 Trophy": 0.5,
    "Womens Under 19 One Day Trophy": 1,
    "Womens Under 19 T20 Challenger Trophy": 0.5,
    "Womens Under 15 One Day Trophy": 1,
    "Senior Womens Multiday": 3,
    "Col Ck Nayudu Trophy Winners vs Roi": 4
}

def get_days_per_match(comp_id, comp_name, match_order):
    """Calculate days based on competition ID and MatchOrder"""
    match_order = (match_order or "").strip().lower()
    
    # Special handling for Ranji Trophy (291)
    if comp_id == 291:
        if any(kw in match_order for kw in ["quarter final", "semi final", "final", "plate - final"]):
            return 5
            
    # Special handling for Vijay Merchant Trophy (298)
    elif comp_id == 298:
        if any(kw in match_order for kw in ["pre quarter final", "quarter final", "semi final", "final", "plate - final"]):
            return 4
    
    # Default to tournament_days mapping
    return tournament_days.get(comp_name, 0)

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Referer": "https://www.bcci.tv/"
}

umpire_days = defaultdict(float)
all_matches = []

def safe_strip(value):
    """Handle None values and clean whitespace"""
    return (value or 'N/A').strip()

def process_competition(comp_id):
    try:
        comp_name = competitions[comp_id]
        #days_per_match = tournament_days[comp_name]
        
        url = f"https://scores.bcci.tv/feeds/{comp_id}-matchschedule.js"
        response = requests.get(url, headers=headers)
        
        # Clean JSONP response
        json_str = re.sub(r'^MatchSchedule\(|\);?\s*$', '', response.text, flags=re.DOTALL)
        data = json.loads(json_str)
        
        # Process matches
        for match in data.get('Matchsummary', []):
            match_order = safe_strip(match.get('MatchOrder'))
            
            # Get days per match with special handling
            days_per_match = get_days_per_match(comp_id, comp_name, match_order)
            
            match_record = {
                "Comp ID": comp_id,
                "Comp Name": comp_name,
                "Match ID": match.get('MatchID'),
                "Match Name": safe_strip(match.get('MatchName')),
                "Match Order": match.get('MatchOrder'),
                "Date": safe_strip(match.get('MatchDate')),
                "First Umpire": safe_strip(match.get('GroundUmpire1')),
                "Second Umpire": safe_strip(match.get('GroundUmpire2')),
                "Third Umpire": safe_strip(match.get('ThirdUmpire')),
                "Venue": safe_strip(match.get('GroundName')),
                #"Result": safe_strip(match.get('Commentss')),
                "Days Per Match": days_per_match
            }
            all_matches.append(match_record)
            
            # Update umpire days
            for umpire in [match_record['First Umpire'], match_record['Second Umpire'], match_record['Third Umpire']]:
                if umpire != 'N/A':
                    umpire_days[umpire] += days_per_match
        
        print(f"Processed {len(data['Matchsummary'])} matches for {comp_name}")
        
    except Exception as e:
        print(f"Error processing {comp_id}: {str(e)}")
    finally:
        time.sleep(3)  # Rate limiting

# Main execution
print("Starting tournament processing...\n")
for comp_id in competitions:
    process_competition(comp_id)

# Export match data
with open('all_matches_1.csv', 'w', newline='', encoding='utf-8') as f:
    writer = csv.DictWriter(f, fieldnames=[
        "Comp ID", "Comp Name", "Match ID", "Match Name", 
        "Match Order","Date", "First Umpire", "Second Umpire", "Third Umpire",
        "Venue", "Days Per Match"
    ])
    writer.writeheader()
    writer.writerows(all_matches)

# Export umpire days
with open('umpire_days.csv', 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)
    writer.writerow(["Umpire", "Total Days"])
    for umpire, days in sorted(umpire_days.items(), key=lambda x: x[1], reverse=True):
        writer.writerow([umpire, round(days, 2)])

print("\nProcessing complete!")
print(f"Total matches processed: {len(all_matches)}")
print(f"Total umpires tracked: {len(umpire_days)}")
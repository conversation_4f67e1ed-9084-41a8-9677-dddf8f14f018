# app.py
"""
Streamlit dashboard for BCCI Umpire/Referee data (based on provided scraper).

Requirements:
  pip install streamlit pandas plotly openpyxl xlsxwriter

Place this file in the same folder as `bcci_upcoming_matches_scraper.py`.
Run: streamlit run app.py
"""

import io
import sys
import os
from datetime import datetime, date
import tempfile
import traceback

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st

# Try to import the provided scraper class
try:
    from bcci_upcoming_matches_scraper import BCCIUpcomingMatchesScraper
except Exception as e:
    BCCIUpcomingMatchesScraper = None
    SCRAPER_IMPORT_ERROR = str(e)

st.set_page_config(page_title="Cricket Umpire/Referee Dashboard", layout="wide")

# -----------------------------
# Utility / Data functions
# -----------------------------
@st.cache_data(ttl=300)
def load_data_via_scraper(auto_run=True, competitions_json=None, genders=None, platforms=None, scan_range=None, date_range=None, month=None):
    """
    Run the provided scraper to obtain a DataFrame, or attempt to load cached Excel/JSON if scraper isn't available.
    Returns a pandas DataFrame.
    """
    # Attempt to run the scraper if available and allowed
    if BCCIUpcomingMatchesScraper and auto_run:
        try:
            scraper = BCCIUpcomingMatchesScraper(
                competitions_json=competitions_json,
                platforms=platforms or ["domestic"],
                genders=genders or ["men", "women"],
                month=month,
                date_range=date_range,
                scan_range=scan_range
            )
            # Use the scraper to build competitions & scrape
            scraper.scrape_all_competitions()
            df = pd.DataFrame(scraper.upcoming_matches)
            if df.empty:
                return df
            # Normalize columns (ensure consistent names)
            # Ensure key columns exist
            expected_cols = [
                'Platform', 'CompetitionGender', 'CompetitionID', 'CompetitionName', 'MatchStatus',
                'MatchOrder', 'MatchID', 'MatchTypeID', 'MatchType', 'MatchName', 'MatchDate',
                'GroundUmpire1', 'GroundUmpire2', 'ThirdUmpire', 'Referee', 'GroundName'
            ]
            # Fill missing expected columns
            for c in expected_cols:
                if c not in df.columns:
                    df[c] = np.nan
            # Parse date
            df['MatchDate_parsed'] = pd.to_datetime(df['MatchDate'], errors='coerce').dt.date
            return df
        except Exception as e:
            st.error("Error running scraper: " + str(e))
            st.exception(traceback.format_exc())
            return pd.DataFrame()
    else:
        # fallback: attempt to load existing excel/JSON exports in cwd
        candidates = ["bcci_upcoming_matches.xlsx", "bcci_upcoming_matches.csv", "bcci_raw_data.json"]
        for f in candidates:
            if os.path.exists(f):
                try:
                    if f.endswith('.xlsx'):
                        df = pd.read_excel(f, sheet_name=0)
                    elif f.endswith('.csv'):
                        df = pd.read_csv(f)
                    else:
                        df = pd.read_json(f)
                    # Normalize date
                    df['MatchDate_parsed'] = pd.to_datetime(df.get('MatchDate', None), errors='coerce').dt.date
                    return df
                except Exception as e:
                    continue
        # if nothing found return empty df
        return pd.DataFrame()

def estimate_days_per_match(row):
    """
    Heuristic to estimate number of officiating days per match.
    If MatchType or MatchOrder indicates multi-day match (First-Class/Test), assume 4 or 5 days.
    Otherwise default to 1 day.
    """
    mt = str(row.get('MatchType') or '').lower()
    mo = str(row.get('MatchOrder') or '').lower()
    # heuristics
    if 'test' in mt or 'test' in mo:
        return 5
    if 'first-class' in mt or 'first-class' in mo or 'first class' in mt:
        # commonly multi-day — assume 4 days by default
        return 4
    # Some competitions e.g. 'Ranji' may be FC: check name
    compname = str(row.get('CompetitionName') or '').lower()
    if 'ranji' in compname or 'duleep' in compname or 'vijay' in compname or 'irani' in compname:
        # assume these are multi-day first-class matches
        return 4
    return 1

def prepare_officials_df(df):
    """
    From matches DataFrame construct officials table: each official (umpire/referee) with counts and sum(days).
    We'll treat GroundUmpire1, GroundUmpire2, ThirdUmpire as 'Umpire' role, and Referee as 'Referee'.
    """
    if df is None or df.empty:
        return pd.DataFrame(columns=['Name', 'Role', 'Matches', 'Days'])
    df_local = df.copy()
    # Ensure days_estimated
    df_local['Days_est'] = df_local.apply(estimate_days_per_match, axis=1)

    rows = []
    # for each match add rows for each official
    for _, r in df_local.iterrows():
        d = int(r['Days_est']) if pd.notna(r['Days_est']) else 1
        # GroundUmpire1
        if pd.notna(r.get('GroundUmpire1')) and r.get('GroundUmpire1') not in ['N/A', '']:
            rows.append({'Name': r['GroundUmpire1'], 'Role': 'Umpire', 'Matches': 1, 'Days': d, 'CompetitionName': r.get('CompetitionName')})
        if pd.notna(r.get('GroundUmpire2')) and r.get('GroundUmpire2') not in ['N/A', '']:
            rows.append({'Name': r['GroundUmpire2'], 'Role': 'Umpire', 'Matches': 1, 'Days': d, 'CompetitionName': r.get('CompetitionName')})
        if pd.notna(r.get('ThirdUmpire')) and r.get('ThirdUmpire') not in ['N/A', '']:
            rows.append({'Name': r['ThirdUmpire'], 'Role': 'Umpire (Third)', 'Matches': 1, 'Days': d, 'CompetitionName': r.get('CompetitionName')})
        if pd.notna(r.get('Referee')) and r.get('Referee') not in ['N/A', '']:
            rows.append({'Name': r['Referee'], 'Role': 'Referee', 'Matches': 1, 'Days': d, 'CompetitionName': r.get('CompetitionName')})
    if not rows:
        return pd.DataFrame(columns=['Name', 'Role', 'Matches', 'Days', 'CompetitionName'])
    officials = pd.DataFrame(rows)
    agg = officials.groupby(['Name', 'Role'], as_index=False).agg({'Matches': 'sum', 'Days': 'sum'})
    return agg.sort_values(['Days', 'Matches'], ascending=[False, False])

def filter_df_by_ui(df, competitions, seasons, officials, date_from, date_to, roles_filter=None):
    """
    Apply UI filters to matches DataFrame.
    competitions: list of names (or empty = all)
    seasons: list of int year
    officials: list of official names
    date_from, date_to: date objects or None
    roles_filter: list like ['Umpire', 'Referee'] to filter matches where any official matches selected roles
    """
    if df is None or df.empty:
        return df
    df_local = df.copy()
    if competitions:
        df_local = df_local[df_local['CompetitionName'].isin(competitions)]
    if seasons:
        df_local = df_local[df_local['MatchDate_parsed'].apply(lambda d: d.year if pd.notna(d) else None).isin(seasons)]
    if date_from:
        df_local = df_local[df_local['MatchDate_parsed'] >= date_from]
    if date_to:
        df_local = df_local[df_local['MatchDate_parsed'] <= date_to]
    if officials:
        # keep matches where any official appears in selected officials
        mask = pd.Series(False, index=df_local.index)
        for col in ['GroundUmpire1', 'GroundUmpire2', 'ThirdUmpire', 'Referee']:
            mask = mask | df_local[col].isin(officials)
        df_local = df_local[mask]
    return df_local

def df_to_excel_bytes(df):
    """Return Excel file bytes for download of dataframe"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='FilteredMatches')
        workbook = writer.book
        worksheet = writer.sheets['FilteredMatches']
        header_format = workbook.add_format({'bold': True, 'align': 'center'})
        for i, col in enumerate(df.columns):
            worksheet.write(0, i, col, header_format)
            width = max(12, min(40, int(df[col].astype(str).str.len().max() + 2)))
            worksheet.set_column(i, i, width)
    output.seek(0)
    return output.read()

# -----------------------------
# App UI
# -----------------------------
st.title("Cricket Umpire/Referee Dashboard")

# Top row: Filters (Competition, Season, Umpire/Referee)
col1, col2, col3, col4 = st.columns([4, 2, 3, 1])

with col4:
    # Auto-refresh option
    auto_refresh = st.checkbox("Auto-refresh (run scraper)", value=False, help="If enabled, the app will run the provided scraper on load. Disable to use local cached exports.")
    # small button to force refresh cache
    if st.button("Refresh Data (clear cache)"):
        st.cache_data.clear()

with col1:
    st.markdown("**Filters**")
    comp_placeholder = st.empty()  # will be replaced after data load

with col2:
    season_placeholder = st.empty()

with col3:
    official_placeholder = st.empty()

# Date range selector at top-right or bottom as in wireframe
date_col_a, date_col_b = st.columns([1, 1])
with date_col_a:
    date_from = st.date_input("From", value=None)
with date_col_b:
    date_to = st.date_input("To", value=None)

# Load data (cached)
# Provide a small status message while loading
with st.spinner("Loading data..."):
    df_matches = load_data_via_scraper(auto_run=auto_refresh)

# If scraper import failed, show a helpful banner
if BCCIUpcomingMatchesScraper is None:
    st.warning("Scraper module couldn't be imported. Falling back to local exports if present. "
               "To enable live scraping place `bcci_upcoming_matches_scraper.py` in same folder. "
               f"Import error: {SCRAPER_IMPORT_ERROR}")

# If empty, show informative message
if df_matches is None or df_matches.empty:
    st.info("No match data available. You can either run the scraper (enable 'Auto-refresh') or place an export "
            "file (bcci_upcoming_matches.xlsx / bcci_raw_data.json) in the app folder.")
    st.stop()

# Normalize some fields
df_matches['CompetitionName'] = df_matches['CompetitionName'].fillna('Unknown Competition')
df_matches['MatchName'] = df_matches['MatchName'].fillna('')
df_matches['MatchDate_parsed'] = pd.to_datetime(df_matches['MatchDate_parsed'], errors='coerce').dt.date

# derive seasons (years)
years = sorted({d.year for d in df_matches['MatchDate_parsed'].dropna()})
if not years:
    years = [datetime.now().year]

# Update filter UI elements now that data is loaded
with col1:
    competitions = sorted(df_matches['CompetitionName'].dropna().unique().tolist())
    selected_comps = st.multiselect("Competition", options=competitions, default=None, help="Select one or more competitions to filter")
with col2:
    selected_seasons = st.multiselect("Season (Year)", options=years, default=None)
with col3:
    # build officials list
    officials_agg = prepare_officials_df(df_matches)
    official_names = officials_agg['Name'].dropna().unique().tolist()
    selected_officials = st.multiselect("Umpire / Referee", options=official_names, default=None, help="Filter by official (searchable)")

# apply date inputs from top; ensure None if empty
date_from_val = date_from if isinstance(date_from, date) and date_from != datetime(1970,1,1).date() else None
date_to_val = date_to if isinstance(date_to, date) and date_to != datetime(1970,1,1).date() else None

# Apply filters
df_filtered = filter_df_by_ui(df_matches, selected_comps or [], selected_seasons or [], selected_officials or [], date_from_val, date_to_val)

# Compute officials table from filtered dataset
officials_filtered = prepare_officials_df(df_filtered)

# Layout: Row 1 charts
r1c1, r1c2 = st.columns([1.1, 1])
with r1c1:
    st.subheader("Total Matches Officiated")
    # Bar chart: total matches per official (umpires + referees combined)
    if not officials_filtered.empty:
        top_officials = officials_filtered.sort_values(['Matches', 'Days'], ascending=[False, False]).head(25)
        fig_bar = px.bar(top_officials.sort_values('Matches', ascending=True),
                         x='Matches', y='Name', orientation='h',
                         hover_data=['Role', 'Days'],
                         labels={'Matches': 'Matches', 'Name': 'Official'})
        fig_bar.update_layout(height=380, margin=dict(l=10, r=10, t=30, b=10))
        st.plotly_chart(fig_bar, use_container_width=True)
    else:
        st.info("No officials match the selected filters.")

with r1c2:
    st.subheader("Days Officiated (stacked by competition)")
    # Build days-per-official-by-competition
    if not df_filtered.empty:
        df_filtered['Days_est'] = df_filtered.apply(estimate_days_per_match, axis=1)
        # Transform to official x competition aggregation
        rows = []
        for _, r in df_filtered.iterrows():
            comp = r.get('CompetitionName', 'Unknown')
            d = int(r.get('Days_est') or 1)
            for name_col in ['GroundUmpire1', 'GroundUmpire2', 'ThirdUmpire']:
                nm = r.get(name_col)
                if pd.notna(nm) and nm not in ['N/A', '']:
                    rows.append({'Official': nm, 'Competition': comp, 'Days': d})
            # referees
            ref = r.get('Referee')
            if pd.notna(ref) and ref not in ['N/A', '']:
                rows.append({'Official': ref, 'Competition': comp, 'Days': d})
        days_df = pd.DataFrame(rows)
        if not days_df.empty:
            days_agg = days_df.groupby(['Official', 'Competition'], as_index=False).agg({'Days': 'sum'})
            # Keep top N officials
            top_officials_names = days_agg.groupby('Official')['Days'].sum().sort_values(ascending=False).head(12).index.tolist()
            days_plot = days_agg[days_agg['Official'].isin(top_officials_names)]
            fig_stack = px.bar(days_plot, x='Official', y='Days', color='Competition',
                               hover_data=['Competition', 'Days'])
            fig_stack.update_layout(barmode='stack', xaxis={'categoryorder':'total descending'}, height=380, margin=dict(l=10, r=10, t=30, b=10))
            st.plotly_chart(fig_stack, use_container_width=True)
        else:
            st.info("No officiating day estimates available for the selected filters.")
    else:
        st.info("No matches for selected filters.")

# Row 2: Competition Coverage (treemap) and Match Official Distribution (pie + leaderboard)
r2c1, r2c2 = st.columns([1, 1])
with r2c1:
    st.subheader("Competition Coverage")
    # Treemap sized by sum of Days (estimated) per competition
    if not df_filtered.empty:
        df_filtered['Days_est'] = df_filtered.apply(estimate_days_per_match, axis=1)
        comp_days = df_filtered.groupby('CompetitionName', as_index=False).agg({'Days_est': 'sum', 'MatchName': 'count'})
        comp_days = comp_days.rename(columns={'Days_est': 'Days', 'MatchName': 'MatchCount'})
        if not comp_days.empty:
            fig_treemap = px.treemap(comp_days, path=['CompetitionName'], values='Days',
                                     hover_data=['MatchCount', 'Days'],
                                     title="")
            fig_treemap.update_layout(margin=dict(l=5, r=5, t=25, b=5), height=420)
            treemap_plot = st.plotly_chart(fig_treemap, use_container_width=True)
            st.caption("Click a treemap block to drill-down to matches for that competition.")
        else:
            st.info("No competition coverage data.")
    else:
        st.info("No matches for selected filters.")

    # Drill-down placeholder: show matches for clicked competition will be handled below using plotly_selected or clickData

with r2c2:
    st.subheader("Match Official Distribution")
    # Build counts: Umpires (ground + third) vs Referees
    if not df_filtered.empty:
        def count_role_occurrences(df):
            umpire_count = 0
            referee_count = 0
            for _, r in df_filtered.iterrows():
                for c in ['GroundUmpire1', 'GroundUmpire2', 'ThirdUmpire']:
                    v = r.get(c)
                    if pd.notna(v) and v not in ['N/A', '']:
                        umpire_count += 1
                if pd.notna(r.get('Referee')) and r.get('Referee') not in ['N/A', '']:
                    referee_count += 1
            return {'Umpire': umpire_count, 'Referee': referee_count}
        distribution = count_role_occurrences(df_filtered)
        dist_df = pd.DataFrame([
            {'Role': 'Umpires (Ground/Third)', 'Count': distribution.get('Umpire', 0)},
            {'Role': 'Referees', 'Count': distribution.get('Referee', 0)}
        ])
        fig_pie = px.pie(dist_df, names='Role', values='Count', hole=0.35)
        fig_pie.update_traces(textposition='inside', textinfo='percent+label')
        fig_pie.update_layout(height=380, margin=dict(l=10, r=10, t=30, b=10))
        st.plotly_chart(fig_pie, use_container_width=True)
    else:
        st.info("No matches for selected filters.")

    # Leaderboard: top 10 officials by Days then Matches
    st.markdown("**Leaderboard — Top Officials**")
    if not officials_filtered.empty:
        leaderboard = officials_filtered.sort_values(['Days', 'Matches'], ascending=[False, False]).head(10)
        leaderboard_display = leaderboard[['Name', 'Role', 'Matches', 'Days']].reset_index(drop=True)
        st.table(leaderboard_display)
    else:
        st.info("No officials to show in leaderboard.")

# Treemap click / drilldown handling
st.markdown("---")
st.subheader("Matches (Drill-down / Details)")

# Capture treemap click via Plotly event if available
# We will create a treemap again with custom id & capture clickData via st.session_state if Plotly emits it
# Simpler approach: offer a selectbox to pick a competition or use treemap click by reading query params (Plotly click events supported in Streamlit via plotly_chart's selected_data?).
# We'll provide both: select competition dropdown and show matches for that selection. If user clicked on treemap, capture click via client-side is limited.
comp_for_detail = st.selectbox("Select Competition to view matches (also used for drill-down)", options=["(All)"] + sorted(df_filtered['CompetitionName'].unique().tolist()))
if comp_for_detail and comp_for_detail != "(All)":
    matches_to_show = df_filtered[df_filtered['CompetitionName'] == comp_for_detail].sort_values('MatchDate_parsed')
else:
    matches_to_show = df_filtered.sort_values('MatchDate_parsed')

# Show table with pagination (st.dataframe is scrollable)
if matches_to_show.empty:
    st.info("No matches to display for the chosen filters.")
else:
    # Provide columns selected like in wireframe
    table_cols = ['MatchDate_parsed', 'CompetitionName', 'MatchName', 'MatchType', 'GroundName', 'GroundUmpire1', 'GroundUmpire2', 'ThirdUmpire', 'Referee']
    present_cols = [c for c in table_cols if c in matches_to_show.columns]
    df_display = matches_to_show[present_cols].rename(columns={'MatchDate_parsed': 'MatchDate'})
    st.dataframe(df_display, use_container_width=True, height=350)

# Bottom: Export & Extras
st.markdown("---")
b1, b2, b3 = st.columns([1, 1, 1])
with b1:
    if st.button("Download Filtered Data as Excel"):
        tosend = df_to_excel_bytes(matches_to_show)
        st.download_button("Click to download", data=tosend, file_name="bcci_filtered_matches.xlsx", mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
with b2:
    auto_refresh_interval = st.number_input("Auto-refresh interval (minutes)", value=60, min_value=1, step=1)
    if auto_refresh and st.button("Force re-run scraper now"):
        # Clear cache and reload
        st.cache_data.clear()
        st.experimental_rerun()
with b3:
    st.markdown("**Notes / Heuristics**")
    st.caption("Days officiated are estimated heuristically (multi-day matches detected via competition/match type). "
               "If you have accurate per-match days, use that field in the input exports for precise Day totals.")

# Footer: small diagnostics
with st.expander("Diagnostics (raw metadata/debug)"):
    st.write(f"Total matches (loaded): {len(df_matches)}")
    st.write(f"Total matches (filtered): {len(df_filtered)}")
    st.write("Sample rows:")
    st.dataframe(df_matches.head(5))

# End
st.success("Dashboard loaded. Use filters above to explore and download filtered data.")

import requests
import csv
import time
from collections import defaultdict

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}

# Initialize data storage
all_matches = []
umpire_days = defaultdict(float)

def process_match(match_id):
    url = "https://scores2.bcci.tv/getDomesticMatchCenterDetails"
    params = {
        "seriesSlug": "ranji trophy",
        "matchID": str(match_id),
        "SERIES_ID": ""
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        data = response.json()
        
        match_data = data.get("postMatch", [{}])[0]
        record = {
            "Match ID": match_id,
            "Competition Name": match_data.get("CompetitionName", "N/A"),
            "Match Name": match_data.get("MatchName", "N/A"),
            "Ground Umpire 1": match_data.get("GroundUmpire1", "N/A"),
            "Ground Umpire 2": match_data.get("GroundUmpire2", "N/A"),
            "Third Umpire": match_data.get("ThirdUmpire", "N/A")
        }
        
        # Update umpire days (0.5 days per match)
        for umpire in [record["Ground Umpire 1"], record["Ground Umpire 2"], record["Third Umpire"]]:
            if umpire and umpire != "N/A":
                umpire_days[umpire] += 4.0
                
        return record
        
    except Exception as e:
        print(f"Error fetching data for Match ID {match_id}: {str(e)}")
        return None

# Process all matches
for match_id in range(13669, 13760):
    match_record = process_match(match_id)
    if match_record:
        all_matches.append(match_record)
    time.sleep(1)  # Maintain polite delay

# Export match details to CSV
with open('umpire_matches_ranji.csv', 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['Match ID', 'Competition Name', 'Match Name', 'Ground Umpire 1', 'Ground Umpire 2', 'Third Umpire']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    writer.writerows(all_matches)

# Export umpire days to CSV
with open('umpire_days_ranji.csv', 'w', newline='', encoding='utf-8') as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(['Umpire Name', 'Total Match Days'])
    
    for umpire, days in sorted(umpire_days.items(), key=lambda x: x[1], reverse=True):
        writer.writerow([umpire, days])

print("Data exported to umpire_matches.csv and umpire_days.csv")
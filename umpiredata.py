import requests
import time
from collections import defaultdict

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "application/json",
    "Referer": "https://www.bcci.tv/",
    "Accept-Language": "en-US,en;q=0.9",
}

umpire_days = defaultdict(float)

def get_match_data(match_id):
    url = "https://scores2.bcci.tv/getDomesticMatchCenterDetails"
    params = {
        "matchID": str(match_id),
        "SERIES_ID": "",
        "seriesSlug": "womens under 23 t20 trophy"  # Reintroduced as it might be required
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # Check for empty response
        if not response.text.strip():
            print(f"Match ID {match_id}: Empty response")
            return None
            
        # Check for HTML responses
        if response.text.strip().startswith("<!DOCTYPE html>"):
            print(f"Match ID {match_id}: Received HTML response (possible block)")
            return None
            
        data = response.json()
        match_data = data.get("postMatch", [{}])[0]
        
        return {
            "Match ID": match_id,
            "Match Name": match_data.get("MatchName", "N/A"),
            "Competition Name": match_data.get("CompetitionName", "N/A"),
            "Ground Umpire 1": match_data.get("GroundUmpire1", "N/A"),
            "Ground Umpire 2": match_data.get("GroundUmpire2", "N/A"),
            "Third Umpire": match_data.get("ThirdUmpire", "N/A")
        }
        
    except Exception as e:
        print(f"Match ID {match_id}: Failed ({type(e).__name__} - {str(e)})")
        return None

# Test range with comprehensive logging
for match_id in [15022, 14920, 14921, 14922, 14923, 14924, 14925]:
    print(f"\nProcessing Match ID {match_id}:")
    result = get_match_data(match_id)
    
    if result:
        print(f"Success: {result}")
        # Update umpire days logic here
    else:
        print("Skipping invalid/match")
    
    time.sleep(2)
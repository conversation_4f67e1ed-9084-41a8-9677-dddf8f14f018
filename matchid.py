import requests
import json
import re
import csv

def scrape_ranji_matches(competition_id=291):
    url = f"https://scores.bcci.tv/feeds/{competition_id}-matchschedule.js"
    
    try:
        response = requests.get(url)
        print(f"Raw response sample: {response.text[:200]}...")  # Debug
        
        # Improved JSONP cleaning
        json_str = re.sub(r'^MatchSchedule\(|\);?\s*$', '', response.text, flags=re.DOTALL)
        
        # Validate JSON
        try:
            data = json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"JSON Error: {e}")
            print(f"Problematic JSON snippet: {json_str[e.pos-50:e.pos+50]}")
            return

        with open(f'competition_{competition_id}.csv', 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow([
                'MatchI D', 'Competition ID', 'Match Name', 'Match Order','Date',
                'Umpire 1', 'Umpire 2', 'Third Umpire', 'Venue'
            ])
            
            for match in data.get('Matchsummary', []):
                writer.writerow([
                    match.get('MatchID'),
                    match.get('CompetitionID'),
                    match.get('MatchName'),
                    match.get('MatchOrder'),
                    match.get('MatchDate'),
                    match.get('GroundUmpire1', 'N/A'),
                    match.get('GroundUmpire2', 'N/A'),
                    match.get('ThirdUmpire', 'N/A'),
                    match.get('GroundName', 'N/A'),
                    #match.get('Commentss', 'N/A').strip()
                ])
                
        print(f"Successfully processed {len(data['Matchsummary'])} matches")

    except Exception as e:
        print(f"Fatal error: {str(e)}")

# Test with Ranji Trophy
scrape_ranji_matches(291)

# competitions = {
#     289: "Duleep Trophy",
#     290: "Irani Cup",
#     291: "Ranji Trophy",
#     292: "Syed Mushtaq Ali Trophy",
#     293: "Vijay Hazare Trophy",
#     294: "Col C K Nayudu Trophy",
#     295: "Mens U23 State A Trophy",
#     296: "Vinoo Mankad Trophy",
#     297: "Cooch Behar Trophy",
#     298: "Vijay Merchant Trophy",
#     299: "VIzzy Trophy",
#     300: "Senior Womens T20 Trophy",
#     301: "Senior Womens T20 Challenger Trophy",
#     302: "Senior Womens One Day Trophy",
#     303: "Senior Womens One Day Challenger Trophy",
#     304: "Womens U23 T20 Trophy",
#     305: "Womens U23 One Day Trophy",
#     306: "Womens Under 19 T20 Trophy",
#     307: "Womens Under 19 One Day Trophy",
#     308: "Womens Under 19 T20 Challenger Trophy",
#     309: "Womens Under 15 One Day Trophy",
#     310: "Senior Womens Multiday",
#     311: "Col Ck Nayudu Trophy Winners vs Roi"

# }

# tournament_days = {
#     "Duleep Trophy": 4,
#     "Irani Cup": 5,
#     "Ranji Trophy": 4,
#     "Syed Mushtaq Ali Trophy": 0.5,
#     "Vijay Hazare Trophy": 1,
#     "Col C K Nayudu Trophy": 4,
#     "Mens U23 State A Trophy": 1,
#     "Vinoo Mankad Trophy": 1,
#     "Cooch Behar Trophy": 4,
#     "Vijay Merchant Trophy": 3,
#     "VIzzy Trophy": 1,
#     "Senior Womens T20 Trophy": 0.5,
#     "Senior Womens T20 Challenger Trophy": 0.5,
#     "Senior Womens One Day Trophy": 1,
#     "Senior Womens One Day Challenger Trophy": 1,
#     "Womens U23 T20 Trophy": 0.5,
#     "Womens U23 One Day Trophy": 1,
#     "Womens Under 19 T20 Trophy": 0.5,
#     "Womens Under 19 One Day Trophy": 1,
#     "Womens Under 19 T20 Challenger Trophy": 0.5,
#     "Womens Under 15 One Day Trophy": 1,
#     "Senior Womens Multiday":3 ,
#     "Col Ck Nayudu Trophy Winners vs Roi": 4

# }
# BCCI Cricket Match Data Scraper

This repository contains Python scripts to scrape upcoming cricket match data from the BCCI (Board of Control for Cricket in India) website.

## Files Created

1. **`bcci_upcoming_matches_scraper.py`** - Complete scraper for all competitions
2. **`bcci_focused_scraper.py`** - Focused scraper with command-line options
3. **`README_BCCI_Scraper.md`** - This documentation file

## Features

### Data Fields Extracted
The scripts extract the following specific fields for each upcoming match:
- **CompetitionID** - Unique identifier for the competition
- **CompetitionName** - Name of the competition (e.g., "Ranji Trophy")
- **MatchOrder** - Match order/round (e.g., "First-Class Match")
- **MatchID** - Unique identifier for the match
- **MatchTypeID** - Type identifier for the match format
- **MatchType** - Match format description (e.g., "Multi Day (4 days)")
- **MatchName** - Teams playing (e.g., "Uttar Pradesh VS Andhra")
- **MatchDate** - Date of the match
- **GroundUmpire1** - First ground umpire
- **GroundUmpire2** - Second ground umpire
- **ThirdUmpire** - Third umpire
- **Referee** - Match referee
- **GroundName** - Venue/ground name

### Supported Competitions
The scraper supports all BCCI domestic competitions:
- Duleep Trophy (289)
- Irani Cup (290)
- Ranji Trophy (291)
- Syed Mushtaq Ali Trophy (292)
- Vijay Hazare Trophy (293)
- Col C K Nayudu Trophy (294)
- Mens U23 State A Trophy (295)
- Vinoo Mankad Trophy (296)
- Cooch Behar Trophy (297)
- Vijay Merchant Trophy (298)
- VIzzy Trophy (299)
- Senior Womens T20 Trophy (300)
- Senior Womens T20 Challenger Trophy (301)
- Senior Womens One Day Trophy (302)
- Senior Womens One Day Challenger Trophy (303)
- Womens U23 T20 Trophy (304)
- Womens U23 One Day Trophy (305)
- Womens Under 19 T20 Trophy (306)
- Womens Under 19 One Day Trophy (307)
- Womens Under 19 T20 Challenger Trophy (308)
- Womens Under 15 One Day Trophy (309)
- Senior Womens Multiday (310)
- Col Ck Nayudu Trophy Winners vs Roi (311)
- Additional Competition (317)

## Usage

### Basic Usage - Complete Scraper

```bash
python bcci_upcoming_matches_scraper.py
```

This will:
- Scrape all competitions for upcoming matches
- Export data to `bcci_upcoming_matches.xlsx`
- Save raw JSON data to `bcci_raw_data.json`
- Display a summary of matches found

### Advanced Usage - Focused Scraper

#### List Available Competitions
```bash
python bcci_focused_scraper.py --list
```

#### Scrape Specific Competitions
```bash
python bcci_focused_scraper.py -c 291 292 317
```

#### Scrape with Custom Output File
```bash
python bcci_focused_scraper.py -c 317 -o my_matches.xlsx
```

#### Command Line Options
- `--competitions` or `-c`: Specify competition IDs to scrape
- `--list` or `-l`: List all available competitions
- `--output` or `-o`: Specify output Excel filename

### Examples

1. **Scrape only Ranji Trophy matches:**
   ```bash
   python bcci_focused_scraper.py -c 291 -o ranji_matches.xlsx
   ```

2. **Scrape multiple specific competitions:**
   ```bash
   python bcci_focused_scraper.py -c 291 292 293 -o major_tournaments.xlsx
   ```

3. **Scrape all competitions (default behavior):**
   ```bash
   python bcci_focused_scraper.py
   ```

## Output

### Excel File Structure
The generated Excel file contains:
- **Sheet Name**: "Upcoming Matches"
- **Headers**: Yellow background, bold, centered
- **Data**: Properly formatted with borders and appropriate column widths
- **Sorting**: Data sorted by CompetitionID and MatchDate

### Sample Output
```
CompetitionID | CompetitionName        | MatchName              | MatchDate  | GroundName
317          | Additional Competition | Uttar Pradesh VS Andhra| 2025-10-15 | Green Park Stadium
317          | Additional Competition | Tamil Nadu VS Jharkhand| 2025-10-15 | Sri Ramakrishna College...
```

## Requirements

### Python Packages
```bash
pip install requests pandas xlsxwriter
```

### Dependencies
- `requests` - For HTTP requests to BCCI API
- `pandas` - For data manipulation and Excel export
- `xlsxwriter` - For Excel formatting
- `json` - For JSON parsing (built-in)
- `re` - For regex operations (built-in)
- `time` - For delays between requests (built-in)

## Technical Details

### Data Source
- **API Endpoint**: `https://scores.bcci.tv/feeds/{competition_id}-matchschedule.js`
- **Format**: JSONP (cleaned to JSON for parsing)
- **Filter**: Only matches with `MatchStatus: "upcoming"` are included

### Rate Limiting
- 1-2 second delay between API requests to be respectful to the server
- Error handling for failed requests

### Data Processing
- Uses the `safe_strip()` function from your existing code to handle None values
- Converts 'N/A' for missing data
- Maintains data integrity with proper error handling

## Customization

### Adding New Competitions
To add new competitions, update the `competitions` dictionary in either script:
```python
self.competitions = {
    # existing competitions...
    999: "New Competition Name"
}
```

### Modifying Output Fields
To add or remove fields, modify the `extract_match_data()` method:
```python
def extract_match_data(self, match, comp_id, comp_name):
    return {
        # existing fields...
        'NewField': safe_strip(match.get('NewFieldName'))
    }
```

## Troubleshooting

### Common Issues
1. **No matches found**: Check if competitions have upcoming matches
2. **HTTP errors**: Verify internet connection and BCCI website availability
3. **JSON parsing errors**: API response format may have changed

### Debug Mode
Use the raw JSON output file to inspect the actual API response structure if needed.

## Notes
- The script focuses specifically on **upcoming matches only**
- Data is automatically sorted by Competition ID and Match Date
- Excel formatting matches your existing code style
- All helper functions from your original code are preserved and reused

import asyncio
from crawl4ai import As<PERSON><PERSON>eb<PERSON>rawler
from crawl4ai.async_configs import <PERSON><PERSON>er<PERSON>onfig, CrawlerRunConfig
from bs4 import BeautifulSoup

async def scrape_drs_data():
    """Scrape DRS data from ESPNCricinfo match."""
    
    # IPL 2025 match URL - SRH vs RR (2nd match)
    scorecard_url = "https://www.espncricinfo.com/series/ipl-2025-1449924/sunrisers-hyderabad-vs-rajasthan-royals-2nd-match-1473439/full-scorecard"
    
    print(f"\nFetching URL: {scorecard_url}")
    
    browser_config = BrowserConfig(
        headless=True,
        browser_type="chromium",
        viewport_width=1280,
        viewport_height=720,
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    )
    
    # Basic run config
    run_config = CrawlerRunConfig()  # Using default configuration since wait_for_timeout is not a valid parameter
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url=scorecard_url,
            config=run_config
        )
        
        if result.html:
            soup = BeautifulSoup(result.html, 'html.parser')
            
            print("\nAnalyzing page content...")
            
            # Find Match Flow section
            match_flow = soup.find(string="Match Flow")
            if match_flow:
                match_flow_section = match_flow.find_parent('div')
                
                print("\nFound Match Flow section!")
                print("\nContent Analysis:")
                print("=" * 50)
                
                # Get all text elements
                all_events = match_flow_section.find_all(["p", "div", "span"])
                
                print(f"\nFound {len(all_events)} events")
                print("-" * 50)
                
                current_innings = ""
                for idx, event in enumerate(all_events, 1):
                    text = event.text.strip()
                    if text:
                        # Check if this is an innings marker
                        if "innings" in text.lower():
                            current_innings = text
                            print(f"\n\n=== {current_innings} ===")
                        else:
                            # Print event details with over information if available
                            over_info = ""
                            if text.startswith("Over"):
                                over_info = f"[{text.split(':')[0]}]"
                            
                            print(f"\nEvent {idx}: {over_info}")
                            print(f"Class: {event.get('class', ['No class'])}")
                            print(f"Text: {text}")
            else:
                print("\nCouldn't find Match Flow section.")
                print("\nAvailable main sections:")
                main_sections = soup.find_all("div", class_="ds-mt-3")
                for section in main_sections:
                    text = section.text.strip()[:100]  # First 100 chars
                    if text:
                        print(f"- {text}...")
        else:
            print("No HTML content received from the page")

if __name__ == "__main__":
    print("ESPNCricinfo Match Flow Content Analysis")
    print("=======================================")
    asyncio.run(scrape_drs_data())









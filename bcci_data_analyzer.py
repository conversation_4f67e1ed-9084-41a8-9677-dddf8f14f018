import pandas as pd
import json
from collections import defaultdict, Counter
import argparse

class BCCIDataAnalyzer:
    def __init__(self, excel_file=None, json_file=None):
        self.df = None
        self.data = None
        
        if excel_file:
            self.load_excel(excel_file)
        elif json_file:
            self.load_json(json_file)
    
    def load_excel(self, filename):
        """Load data from Excel file"""
        try:
            self.df = pd.read_excel(filename)
            print(f"Loaded {len(self.df)} matches from {filename}")
        except Exception as e:
            print(f"Error loading Excel file: {e}")
    
    def load_json(self, filename):
        """Load data from JSON file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            self.df = pd.DataFrame(self.data)
            print(f"Loaded {len(self.df)} matches from {filename}")
        except Exception as e:
            print(f"Error loading JSON file: {e}")
    
    def competition_summary(self):
        """Show summary by competition"""
        if self.df is None:
            print("No data loaded")
            return
        
        print("\n=== COMPETITION SUMMARY ===")
        comp_summary = self.df.groupby(['CompetitionID', 'CompetitionName']).size().reset_index(name='MatchCount')
        comp_summary = comp_summary.sort_values('MatchCount', ascending=False)
        
        for _, row in comp_summary.iterrows():
            print(f"{row['CompetitionName']} (ID: {row['CompetitionID']}): {row['MatchCount']} matches")
    
    def umpire_analysis(self):
        """Analyze umpire assignments"""
        if self.df is None:
            print("No data loaded")
            return
        
        print("\n=== UMPIRE ANALYSIS ===")
        
        # Collect all umpires
        all_umpires = []
        for col in ['GroundUmpire1', 'GroundUmpire2', 'ThirdUmpire']:
            umpires = self.df[col].dropna()
            umpires = umpires[umpires != 'N/A']
            all_umpires.extend(umpires.tolist())
        
        umpire_counts = Counter(all_umpires)
        
        print(f"Total unique umpires: {len(umpire_counts)}")
        print("\nTop 10 most assigned umpires:")
        for umpire, count in umpire_counts.most_common(10):
            print(f"  {umpire}: {count} assignments")
    
    def referee_analysis(self):
        """Analyze referee assignments"""
        if self.df is None:
            print("No data loaded")
            return
        
        print("\n=== REFEREE ANALYSIS ===")
        
        referees = self.df['Referee'].dropna()
        referees = referees[referees != 'N/A']
        referee_counts = Counter(referees.tolist())
        
        print(f"Total unique referees: {len(referee_counts)}")
        print("\nTop 10 most assigned referees:")
        for referee, count in referee_counts.most_common(10):
            print(f"  {referee}: {count} assignments")
    
    def venue_analysis(self):
        """Analyze venue usage"""
        if self.df is None:
            print("No data loaded")
            return
        
        print("\n=== VENUE ANALYSIS ===")
        
        venues = self.df['GroundName'].dropna()
        venues = venues[venues != 'N/A']
        venue_counts = Counter(venues.tolist())
        
        print(f"Total unique venues: {len(venue_counts)}")
        print("\nTop 10 most used venues:")
        for venue, count in venue_counts.most_common(10):
            print(f"  {venue}: {count} matches")
    
    def match_type_analysis(self):
        """Analyze match types"""
        if self.df is None:
            print("No data loaded")
            return
        
        print("\n=== MATCH TYPE ANALYSIS ===")
        
        match_types = self.df['MatchType'].value_counts()
        
        print("Match types distribution:")
        for match_type, count in match_types.items():
            print(f"  {match_type}: {count} matches")
    
    def date_analysis(self):
        """Analyze match dates"""
        if self.df is None:
            print("No data loaded")
            return
        
        print("\n=== DATE ANALYSIS ===")
        
        # Convert MatchDate to datetime
        try:
            self.df['MatchDate'] = pd.to_datetime(self.df['MatchDate'])
            
            # Group by date
            date_counts = self.df['MatchDate'].value_counts().sort_index()
            
            print(f"Matches scheduled from {date_counts.index.min().strftime('%Y-%m-%d')} to {date_counts.index.max().strftime('%Y-%m-%d')}")
            print(f"\nDates with most matches:")
            for date, count in date_counts.head(10).items():
                print(f"  {date.strftime('%Y-%m-%d')}: {count} matches")
                
        except Exception as e:
            print(f"Error analyzing dates: {e}")
    
    def missing_data_analysis(self):
        """Analyze missing data"""
        if self.df is None:
            print("No data loaded")
            return
        
        print("\n=== MISSING DATA ANALYSIS ===")
        
        for column in self.df.columns:
            na_count = (self.df[column] == 'N/A').sum()
            null_count = self.df[column].isnull().sum()
            total_missing = na_count + null_count
            percentage = (total_missing / len(self.df)) * 100
            
            if total_missing > 0:
                print(f"{column}: {total_missing} missing ({percentage:.1f}%)")
    
    def export_analysis_report(self, filename="bcci_analysis_report.txt"):
        """Export analysis to text file"""
        import sys
        from io import StringIO
        
        # Capture print output
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        # Run all analyses
        self.competition_summary()
        self.umpire_analysis()
        self.referee_analysis()
        self.venue_analysis()
        self.match_type_analysis()
        self.date_analysis()
        self.missing_data_analysis()
        
        # Get the output
        output = captured_output.getvalue()
        sys.stdout = old_stdout
        
        # Write to file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("BCCI UPCOMING MATCHES ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(output)
        
        print(f"Analysis report exported to {filename}")
    
    def run_full_analysis(self):
        """Run all analysis functions"""
        self.competition_summary()
        self.umpire_analysis()
        self.referee_analysis()
        self.venue_analysis()
        self.match_type_analysis()
        self.date_analysis()
        self.missing_data_analysis()

def main():
    parser = argparse.ArgumentParser(description='BCCI Match Data Analyzer')
    parser.add_argument('--excel', '-e', help='Excel file to analyze')
    parser.add_argument('--json', '-j', help='JSON file to analyze')
    parser.add_argument('--report', '-r', help='Export analysis report to file')
    parser.add_argument('--competition', '-c', action='store_true', help='Show competition summary only')
    parser.add_argument('--umpires', '-u', action='store_true', help='Show umpire analysis only')
    parser.add_argument('--referees', action='store_true', help='Show referee analysis only')
    parser.add_argument('--venues', '-v', action='store_true', help='Show venue analysis only')
    
    args = parser.parse_args()
    
    if not args.excel and not args.json:
        # Default to most recent Excel file
        args.excel = 'bcci_upcoming_matches.xlsx'
    
    # Initialize analyzer
    analyzer = BCCIDataAnalyzer(excel_file=args.excel, json_file=args.json)
    
    if analyzer.df is None:
        print("Failed to load data. Please check file path.")
        return
    
    # Run specific analyses or full analysis
    if args.competition:
        analyzer.competition_summary()
    elif args.umpires:
        analyzer.umpire_analysis()
    elif args.referees:
        analyzer.referee_analysis()
    elif args.venues:
        analyzer.venue_analysis()
    else:
        analyzer.run_full_analysis()
    
    # Export report if requested
    if args.report:
        analyzer.export_analysis_report(args.report)

if __name__ == "__main__":
    main()

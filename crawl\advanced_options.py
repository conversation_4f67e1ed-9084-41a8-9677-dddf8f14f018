"""
Advanced Options Example

This script demonstrates advanced options for crawl4ai including:
- Using proxies
- Custom headers
- Handling cookies
- Executing JavaScript
- Taking screenshots
"""

import async<PERSON>
import os
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import B<PERSON>erConfig, CrawlerRunConfig

# Create output directory
OUTPUT_DIR = "advanced_output"
os.makedirs(OUTPUT_DIR, exist_ok=True)

async def proxy_example():
    """Example of using a proxy with crawl4ai."""
    print("\n=== Proxy Example ===")
    
    # Note: Replace with a working proxy or this example will fail
    proxy = {
        "server": "http://your-proxy-server:port",
        # Uncomment if your proxy requires authentication
        # "username": "your-username",
        # "password": "your-password",
    }
    
    browser_config = BrowserConfig(
        headless=True,
        proxy=proxy,  # Set proxy configuration
    )
    
    run_config = CrawlerRunConfig()
    
    try:
        async with AsyncWebCrawler(config=browser_config) as crawler:
            # Check IP to verify proxy is working
            result = await crawler.arun(
                url="https://api.ipify.org?format=json",
                config=run_config
            )
            print(f"IP Address with proxy: {result.text}")
    except Exception as e:
        print(f"Proxy example failed: {e}")
        print("Note: This example requires a working proxy configuration")

async def custom_headers_example():
    """Example of using custom headers with crawl4ai."""
    print("\n=== Custom Headers Example ===")
    
    browser_config = BrowserConfig(headless=True)
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Set custom headers
        await crawler.set_extra_http_headers({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept-Language": "en-US,en;q=0.9",
            "X-Custom-Header": "Crawl4AI Example",
        })
        
        # Visit a site that displays headers
        result = await crawler.arun(
            url="https://httpbin.org/headers",
            config=run_config
        )
        
        print(f"Response with custom headers: {result.text}")

async def cookie_handling():
    """Example of handling cookies with crawl4ai."""
    print("\n=== Cookie Handling Example ===")
    
    browser_config = BrowserConfig(headless=True)
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Set a cookie
        await crawler.add_cookies([{
            "name": "example_cookie",
            "value": "crawl4ai_test",
            "domain": "httpbin.org",
            "path": "/",
        }])
        
        # Visit a site that displays cookies
        result = await crawler.arun(
            url="https://httpbin.org/cookies",
            config=run_config
        )
        
        print(f"Response with cookies: {result.text}")
        
        # Get all cookies
        cookies = await crawler.get_cookies()
        print(f"All cookies: {cookies}")

async def javascript_execution():
    """Example of executing JavaScript with crawl4ai."""
    print("\n=== JavaScript Execution Example ===")
    
    browser_config = BrowserConfig(headless=True)
    run_config = CrawlerRunConfig()
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Navigate to a page
        await crawler.goto("https://example.com")
        
        # Execute JavaScript to modify the page
        await crawler.evaluate_javascript("""
            // Change the title
            document.title = 'Modified by Crawl4AI';
            
            // Change the heading
            const heading = document.querySelector('h1');
            if (heading) {
                heading.textContent = 'Modified Heading';
                heading.style.color = 'red';
            }
            
            // Add a new paragraph
            const p = document.createElement('p');
            p.textContent = 'This paragraph was added by JavaScript execution in Crawl4AI';
            document.body.appendChild(p);
        """)
        
        # Take a screenshot of the modified page
        screenshot_path = os.path.join(OUTPUT_DIR, "js_modified.png")
        await crawler.screenshot(screenshot_path)
        
        print(f"Modified page screenshot saved to: {screenshot_path}")
        
        # Get the modified HTML
        html = await crawler.get_content()
        html_path = os.path.join(OUTPUT_DIR, "js_modified.html")
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html)
        
        print(f"Modified HTML saved to: {html_path}")

async def screenshot_example():
    """Example of taking screenshots with crawl4ai."""
    print("\n=== Screenshot Example ===")
    
    browser_config = BrowserConfig(
        headless=True,
        viewport_width=1280,
        viewport_height=800,
    )
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Navigate to a page
        await crawler.goto("https://github.com/unclecode/crawl4ai")
        
        # Take a full page screenshot
        full_screenshot_path = os.path.join(OUTPUT_DIR, "full_page.png")
        await crawler.screenshot(full_screenshot_path, full_page=True)
        print(f"Full page screenshot saved to: {full_screenshot_path}")
        
        # Take a screenshot of a specific element
        element_screenshot_path = os.path.join(OUTPUT_DIR, "element.png")
        await crawler.screenshot(
            element_screenshot_path,
            selector=".repository-content",  # GitHub repository content area
        )
        print(f"Element screenshot saved to: {element_screenshot_path}")

async def main():
    """Main function to run all examples."""
    print("Advanced Options Examples")
    print("========================")
    
    # Uncomment to run the proxy example (requires a working proxy)
    # await proxy_example()
    
    await custom_headers_example()
    await cookie_handling()
    await javascript_execution()
    await screenshot_example()

if __name__ == "__main__":
    asyncio.run(main())
